@import "../../styles/variables.scss";

.dailyLife {
  background: #FCFCF8;
}

.content {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap-padding) * 1.5);
}

.textSection {
  display: flex;
  flex-direction: column;
  gap: calc(var(--gap-padding) / 2);
}

.title {
  font-size: clamp(2rem, 4vw + 1rem, 3.5rem);
  font-weight: 600;
  line-height: 1.2;
  margin: 0;
  color: var(--color-text);
}

.description {
  font-size: clamp(1.125rem, 1.5vw + 0.5rem, 1.375rem);
  line-height: 1.6;
  color: var(--color-text-secondary);
  margin: 0;
  max-width: 60ch;
}

.imagesSection {
  display: flex;
  flex-direction: column;
  gap: var(--gap-padding);
}

.imageWrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  border-radius: 8px;
}

.image {
  width: 100%;
  height: auto;
  object-fit: cover;
  display: block;
}

// Tablet - 768px+
@media (min-width: $breakpoint-sm) {
  .imagesSection {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--gap-padding);
  }
}

// Desktop - 1200px+
@media (min-width: $breakpoint-lg) {
  .content {
    gap: calc(var(--gap-padding) * 2);
  }
  
  .description {
    max-width: 50ch;
  }
}
