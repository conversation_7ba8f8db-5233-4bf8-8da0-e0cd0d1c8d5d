import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';
import { motion } from 'framer-motion';
import TextReveal from '@/components/TextReveal';

export default function DailyLife({ 
  locale = 'fr',
  titleKey = 'agency.daily_life.title',
  descriptionKey = 'agency.daily_life.description',
  namespace = 'pages'
}) {
  const { t } = useTranslation(namespace);

  return (
    <section className={`${styles.dailyLife} section`}>
      <div className="container">
        <div className={styles.content}>
          <div className={styles.textSection}>
            <TextReveal as="h2" className={styles.title}>
              {t(titleKey)}
            </TextReveal>
            
            <motion.p
              className={styles.description}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.3 }}
            >
              {t(description<PERSON><PERSON>)}
            </motion.p>
          </div>

          <div className={styles.imagesSection}>
            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.4 }}
            >
              <Image
                src="/images/ordinateur-bureau-macbook.png"
                alt={t('agency.daily_life.image1_alt')}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>

            <motion.div
              className={styles.imageWrapper}
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.5 }}
            >
              <Image
                src="/images/lucas-joliveau-telephone-assis.png"
                alt={t('agency.daily_life.image2_alt')}
                width={600}
                height={400}
                className={styles.image}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
