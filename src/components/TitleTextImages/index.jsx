import React from 'react';
import Image from 'next/image';
import styles from './style.module.scss';
import { useTranslation } from '@/hooks/useTranslation';
import { motion } from 'framer-motion';
import TextReveal from '@/components/TextReveal';

export default function TitleTextImages({
  locale = 'fr',
  titleKey = 'agency.daily_life.title',
  descriptionKey = 'agency.daily_life.description',
  namespace = 'pages',
  image1Src = '/images/ordinateur-bureau-macbook.png',
  image2Src = '/images/lucas-joliveau-telephone-assis.png',
  image1AltKey = 'agency.daily_life.image1_alt',
  image2AltKey = 'agency.daily_life.image2_alt'
}) {
  const { t } = useTranslation(namespace);

  return (
    <section className={`${styles.titleTextImages} section`}>
      <div className="container">
        <div className={styles.content}>
          <div className={styles.textSection}>
            <TextReveal as="h2" className={styles.title}>
              {t(titleKey)}
            </TextReveal>
            
            <motion.p
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.2 }}
            >
              {t(descriptionKey)}
            </motion.p>
          </div>

          <div className={styles.imagesSection}>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.1 }}
            >
              <Image
                src="/images/lucas-joliveau-siege-ordinateur-portable.png"
                alt={t(image1AltKey)}
                width={600}
                height={400}
              />
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, ease: 'easeInOut', delay: 0.2 }}
            >
              <Image
                src={image2Src}
                alt={t(image2AltKey)}
                width={600}
                height={400}
              />
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
}
