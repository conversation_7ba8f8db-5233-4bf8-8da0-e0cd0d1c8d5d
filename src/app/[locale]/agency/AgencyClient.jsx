"use client";

import Hero3 from '@/components/Heros/Hero3';
import StatsCards from '@/components/StatsCards';
import ThreeColumnTitle from '@/components/ThreeColumnTitle';
import ExpertiseAccordion from '@/components/ExpertiseAccordion';
import Testimonials from '@/components/Testimonials';
import Values from '@/components/Values';
import DailyLife from '@/components/DailyLife';

export default function AgencyClient({ params }) {
  const locale = params.locale || 'fr';

  // Données des expertises (en attendant une meilleure solution de traduction)
  const expertisesData = locale === 'fr' ? [
    {
      title: "Création et refonte d'identité",
      items: [
        "Création de logotype",
        "Design de marchandises",
        "Impression sur-mesure"
      ]
    },
    {
      title: "Stratégie et design thinking",
      items: [
        "Analyse concurrentielle",
        "Définition de personas",
        "Parcours utilisateur"
      ]
    },
    {
      title: "Développement web",
      items: [
        "Sites vitrine",
        "E-commerce",
        "Applications web"
      ]
    }
  ] : [
    {
      title: "Brand identity creation and redesign",
      items: [
        "Logo creation",
        "Merchandise design",
        "Custom printing"
      ]
    },
    {
      title: "Strategy and design thinking",
      items: [
        "Competitive analysis",
        "Persona definition",
        "User journey"
      ]
    },
    {
      title: "Web development",
      items: [
        "Showcase websites",
        "E-commerce",
        "Web applications"
      ]
    }
  ];

  return (
    <div>
      <div className="container">
        <Hero3
          label="À propos"
          title="Nous sommes une agence de design et de web basée en Rive-Sud de Montréal."
          description="Avec plus de 8 ans d’expérience, nous imaginons, développons et livrons des projets où chaque détail compte, pour créer un impact et marquer les esprits."
          buttonText="Découvrir nos projets"
          buttonLink="https://g.page/r/CdYePvCaFA87EAE/review"
        />
      </div>
            <ExpertiseAccordion
        title={locale === 'fr' ? 'Nos expertises' : 'Our expertise'}
        expertises={expertisesData}
      />


      <ThreeColumnTitle locale={locale} />
      <DailyLife locale={locale} />
      <Values />
      <StatsCards />
      <Testimonials locale={locale} />
    </div>
  );
}
